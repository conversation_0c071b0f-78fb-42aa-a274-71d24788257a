@echo off
chcp 65001 >nul 2>&1
title Oracle 自動化效能修復工具

echo.
echo ⚡ Oracle 資料庫自動化效能修復工具 ⚡
echo =====================================
echo.
echo 🎯 目標: 解決 SQL 查詢從 10000 秒降到 10 秒
echo 🤖 方式: 全自動診斷 + 修復 + 驗證
echo.
echo 📊 將要執行的修復:
echo   ✅ 自動建立關鍵索引 (日期欄位)
echo   ✅ 建立年度摘要表 (快速查詢)
echo   ✅ 更新統計資訊 (優化執行計畫)
echo   ✅ 生成優化查詢腳本
echo   ✅ 驗證效能改善
echo.
echo ⚠️  注意事項:
echo   - 此工具會自動修改資料庫結構 (建立索引和表格)
echo   - 建議在非營業時間執行
echo   - 整個過程約需 30-60 分鐘
echo.

set /p confirm="確定要開始自動修復嗎? (Y/N): "
if /i not "%confirm%"=="Y" (
    echo 取消執行
    pause
    exit /b 0
)

echo.
echo 🚀 開始自動修復...
echo ==================

REM 記錄開始時間
echo 開始時間: %date% %time%

REM 執行自動診斷和修復
python database_diagnosis.py

REM 記錄結束時間
echo.
echo 結束時間: %date% %time%

echo.
echo ================================================
echo 🎉 自動修復完成！
echo ================================================
echo.
echo 📊 效能改善預期:
echo   原始查詢: 10000 秒 (約 2.8 小時)
echo   修復後:   10-30 秒
echo   改善幅度: 99%+
echo.
echo 📁 生成的檔案:
echo   📋 performance_report.txt - 詳細診斷報告
echo   🚀 optimized_queries_auto.sql - 優化後的查詢
echo   📝 db_diagnosis.log - 執行日誌
echo.
echo 🔄 後續步驟:
echo   1. 查看 performance_report.txt 了解修復詳情
echo   2. 使用 optimized_queries_auto.sql 中的查詢替代原始查詢
echo   3. 測試新查詢的執行時間
echo   4. 定期執行此工具進行維護
echo.

REM 自動開啟報告檔案
if exist "performance_report.txt" (
    echo 正在開啟診斷報告...
    start notepad "performance_report.txt"
)

if exist "optimized_queries_auto.sql" (
    echo 正在開啟優化查詢...
    start notepad "optimized_queries_auto.sql"
)

echo.
echo 按任意鍵結束...
pause >nul

@echo off
chcp 65001 >nul 2>&1
echo Installing Python packages for Oracle database diagnosis...
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python first: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python version:
python --version

echo.
echo Installing required packages...

REM Install cx_Oracle
echo Installing cx_Oracle...
pip install cx_Oracle

REM Install pandas
echo Installing pandas...
pip install pandas

REM Install other dependencies
echo Installing additional packages...
pip install numpy

echo.
echo Installation completed!
echo.
echo You can now run the diagnosis tool:
echo python database_diagnosis.py
echo.
pause

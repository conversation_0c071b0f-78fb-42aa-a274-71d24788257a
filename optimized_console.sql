-- 優化後的 SQL 查詢
-- 主要優化點：
-- 1. 使用 WITH 子句提高可讀性和效能
-- 2. 減少重複的日期轉換計算
-- 3. 優化 JOIN 條件
-- 4. 建議索引策略

-- 建議建立的索引：
-- CREATE INDEX idx_asab_date_venn ON asab(abdate, abvenn, abcstn, abitno);
-- CREATE INDEX idx_asfa_date_venn ON asfa(fadate, favenn, facstn, farout);
-- CREATE INDEX idx_asff_date_venn ON asff(ffdate, ffvenn);
-- CREATE INDEX idx_asaa_venn_cstn ON asaa(aavenn, aacstn, aaitem);
-- CREATE INDEX idx_ashc_venn_cstn ON ashc(hcvenn, hccstn, hcrout);
-- CREATE INDEX idx_btba_yymm_itno ON btba(bayymm, baitno);
-- CREATE INDEX idx_sbai_itno ON sbai(aiitno);

WITH 
-- 定義日期範圍常數，避免重複計算
date_range AS (
    SELECT 
        ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AS start_date,
        SYSDATE AS end_date
    FROM dual
),

-- 優化 ASAB 資料查詢
asab_data AS (
    SELECT 
        SUBSTR(a.abdate, 1, 5) AS abyymm,
        a.abvenn,
        c.aaitem AS trade_type,
        a.abdate,
        c.aadate,
        c.aastop,
        c.aarout,
        a.abitno,
        COALESCE(b.aiitno2, a.abitno) AS abitno2,
        SUM(a.aboqty) AS aboqty,
        SUM(a.abtxat) AS abtxat,
        SUM(a.abamte) AS abamte,
        SUM(a.abrqty) AS abrqty,
        SUM(a.abrtxa) AS abrtxa,
        SUM(a.abramt) AS abramt,
        SUM(a.abpqty) AS abpqty,
        a.aborsd,
        CASE a.abtype WHEN 'V' THEN 'VM' ELSE 'O' END AS abtype,
        SUM(a.aboqty + a.abpqty - a.abrqty) AS qty
    FROM asab a
    INNER JOIN asaa c ON (a.abvenn = c.aavenn AND a.abcstn = c.aacstn)
    LEFT JOIN sbai b ON (a.abitno = b.aiitno)
    CROSS JOIN date_range dr
    WHERE c.aaitem IS NOT NULL
      AND TO_DATE(
            TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) 
            || SUBSTR(a.abdate, 4, 4), 
            'yyyymmdd'
          ) BETWEEN dr.start_date AND dr.end_date
    GROUP BY 
        SUBSTR(a.abdate, 1, 5),
        a.abvenn,
        c.aaitem,
        a.abdate,
        c.aadate,
        c.aastop,
        c.aarout,
        a.abitno,
        COALESCE(b.aiitno2, a.abitno),
        a.aborsd,
        CASE a.abtype WHEN 'V' THEN 'VM' ELSE 'O' END
),

-- 優化 ASFA 資料查詢
asfa_data AS (
    SELECT 
        SUBSTR(a.fadate, 1, 5) AS abyymm,
        a.favenn AS abvenn,
        c.hcitem AS trade_type,
        a.fadate AS abdate,
        c.HCCRDT AS aadate,
        c.HCSTDT AS aastop,
        c.HCROUT AS aarout,
        NULL AS abitno,
        a.faitno AS abitno2,
        SUM(a.fasqty) AS aboqty,
        SUM(a.faamte) AS abtxat,
        0 AS abamte,
        0 AS abrqty,
        0 AS abrtxa,
        0 AS abramt,
        0 AS abpqty,
        NULL AS aborsd,
        NULL AS abtype,
        SUM(a.fasqty) AS qty
    FROM asfa a
    INNER JOIN ashc c ON (a.favenn = c.hcvenn AND a.facstn = c.hccstn AND a.farout = c.hcrout)
    CROSS JOIN date_range dr
    WHERE TO_DATE(
            TO_CHAR(TO_NUMBER(SUBSTR(a.fadate, 1, 3)) + 1911) 
            || SUBSTR(a.fadate, 4, 4), 
            'yyyymmdd'
          ) BETWEEN dr.start_date AND dr.end_date
    GROUP BY 
        SUBSTR(a.fadate, 1, 5),
        a.favenn,
        c.hcitem,
        a.fadate,
        c.HCCRDT,
        c.HCSTDT,
        c.HCROUT,
        a.faitno
),

-- 優化 ASFF 資料查詢
asff_data AS (
    SELECT 
        SUBSTR(a.ffdate, 1, 5) AS abyymm,
        a.ffvenn AS abvenn,
        c.hcitem AS trade_type,
        a.ffdate AS abdate,
        c.HCCRDT AS aadate,
        c.HCSTDT AS aastop,
        c.HCROUT AS aarout,
        NULL AS abitno,
        a.ffitno AS abitno2,
        SUM(a.ffoqty * 12) AS aboqty, -- 將打數轉換成瓶罐
        0 AS abtxat,
        SUM(a.ffamte) AS abamte,
        0 AS abrqty,
        0 AS abrtxa,
        0 AS abramt,
        0 AS abpqty,
        NULL AS aborsd,
        'EX' AS abtype,
        SUM(a.ffoqty * 12) AS qty -- 將打數轉換成瓶罐
    FROM asff a
    INNER JOIN ashc c ON (a.ffvenn = c.hcvenn)
    CROSS JOIN date_range dr
    WHERE TO_DATE(
            TO_CHAR(TO_NUMBER(SUBSTR(a.ffdate, 1, 3)) + 1911) 
            || SUBSTR(a.ffdate, 4, 4), 
            'yyyymmdd'
          ) BETWEEN dr.start_date AND dr.end_date
    GROUP BY 
        SUBSTR(a.ffdate, 1, 5),
        a.ffvenn,
        c.hcitem,
        a.ffdate,
        c.HCCRDT,
        c.HCSTDT,
        c.HCROUT,
        a.ffitno
),

-- 合併所有資料
combined_data AS (
    SELECT * FROM asab_data
    UNION ALL
    SELECT * FROM asfa_data
    UNION ALL
    SELECT * FROM asff_data
),

-- 與價格資料 JOIN
final_data AS (
    SELECT 
        TO_DATE(
            TO_CHAR(TO_NUMBER(SUBSTR(c.abyymm, 1, 3)) + 1911) 
            || SUBSTR(c.abyymm, 4, 2) || '01', 
            'yyyymmdd'
        ) AS abyymm,
        c.abvenn,
        c.abdate,
        c.abitno2 AS abitno,
        c.aboqty,
        c.abtxat,
        c.abamte,
        c.abrqty,
        c.abrtxa,
        c.abramt,
        c.abpqty,
        c.aborsd,
        c.trade_type,
        c.aadate,
        c.aastop,
        NULL AS aakind,
        NULL AS aadisp,
        c.aarout,
        ROUND((ROUND((c.qty * b.bapric) / b.barate, 0) * (1 - b.barat1)), 0) AS amt
    FROM combined_data c
    INNER JOIN btba b ON (c.abyymm = b.bayymm AND c.abitno2 = b.baitno)
    CROSS JOIN date_range dr
    WHERE TO_DATE(
            TO_CHAR(TO_NUMBER(SUBSTR(c.abdate, 1, 3)) + 1911) 
            || SUBSTR(c.abdate, 4, 4), 
            'yyyymmdd'
          ) BETWEEN dr.start_date AND dr.end_date
)

-- 最終查詢結果
SELECT 
    abyymm,
    abvenn,
    TO_DATE(
        TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) 
        || SUBSTR(abdate, 4, 2) 
        || SUBSTR(abdate, 6, 2), 
        'yyyymmdd'
    ) AS abdate,
    abitno,
    aboqty,
    abtxat,
    abamte,
    abrqty,
    abrtxa,
    abramt,
    abpqty,
    aborsd,
    trade_type,
    NULL AS aastop,
    NULL,
    NULL,
    NULL,
    aakind,
    aadisp,
    aarout,
    SUM(amt) AS amt,
    TO_CHAR(abyymm, 'YYYYMM') || abitno AS yymm_abitno,
    abvenn || aarout || trade_type AS abvenn_aarout_type
FROM final_data
GROUP BY 
    abyymm,
    abvenn,
    TO_DATE(
        TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) 
        || SUBSTR(abdate, 4, 2) 
        || SUBSTR(abdate, 6, 2), 
        'yyyymmdd'
    ),
    abitno,
    aboqty,
    abtxat,
    abamte,
    abrqty,
    abrtxa,
    abramt,
    abpqty,
    aborsd,
    trade_type,
    aakind,
    aadisp,
    aarout,
    TO_CHAR(abyymm, 'YYYYMM') || abitno,
    abvenn || aarout || trade_type;

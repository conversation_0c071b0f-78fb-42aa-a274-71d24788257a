-- 索引建立腳本
-- 建議在非營業時間執行，建立索引可能需要較長時間

-- 檢查現有索引
SELECT index_name, table_name, column_name, column_position
FROM user_ind_columns 
WHERE table_name IN ('ASAB', 'ASFA', 'ASFF', 'ASAA', 'ASHC', 'BTBA', 'SBAI')
ORDER BY table_name, index_name, column_position;

-- 1. ASAB 表索引（最重要，資料量最大）
-- 複合索引：日期 + 廠商 + 客戶 + 產品
CREATE INDEX idx_asab_date_venn_cstn_itno ON asab(abdate, abvenn, abcstn, abitno) 
PARALLEL 4 NOLOGGING;

-- 日期範圍查詢專用索引
CREATE INDEX idx_asab_date_calc ON asab(
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd')
) PARALLEL 4 NOLOGGING;

-- 年月分組索引
CREATE INDEX idx_asab_yymm ON asab(SUBSTR(abdate, 1, 5)) PARALLEL 4 NOLOGGING;

-- 2. ASFA 表索引
CREATE INDEX idx_asfa_date_venn_cstn ON asfa(fadate, favenn, facstn, farout) 
PARALLEL 4 NOLOGGING;

CREATE INDEX idx_asfa_date_calc ON asfa(
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(fadate, 1, 3)) + 1911) || SUBSTR(fadate, 4, 4), 'yyyymmdd')
) PARALLEL 4 NOLOGGING;

CREATE INDEX idx_asfa_yymm ON asfa(SUBSTR(fadate, 1, 5)) PARALLEL 4 NOLOGGING;

-- 3. ASFF 表索引（資料量小，但仍需要）
CREATE INDEX idx_asff_date_venn ON asff(ffdate, ffvenn) PARALLEL 2 NOLOGGING;

CREATE INDEX idx_asff_date_calc ON asff(
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ffdate, 1, 3)) + 1911) || SUBSTR(ffdate, 4, 4), 'yyyymmdd')
) PARALLEL 2 NOLOGGING;

-- 4. 關聯表索引
-- ASAA 客戶主檔
CREATE INDEX idx_asaa_venn_cstn_item ON asaa(aavenn, aacstn, aaitem) PARALLEL 2 NOLOGGING;

-- ASHC 客戶主檔
CREATE INDEX idx_ashc_venn_cstn_rout ON ashc(hcvenn, hccstn, hcrout) PARALLEL 2 NOLOGGING;

-- BTBA 價格主檔
CREATE INDEX idx_btba_yymm_itno ON btba(bayymm, baitno) PARALLEL 2 NOLOGGING;

-- SBAI 產品轉換主檔
CREATE INDEX idx_sbai_itno ON sbai(aiitno) PARALLEL 2 NOLOGGING;

-- 5. 額外的效能索引
-- ASAB 按類型分組
CREATE INDEX idx_asab_type ON asab(abtype) PARALLEL 2 NOLOGGING;

-- 統計資訊更新
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASAB', CASCADE => TRUE);
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASFA', CASCADE => TRUE);
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASFF', CASCADE => TRUE);
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASAA', CASCADE => TRUE);
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASHC', CASCADE => TRUE);
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'BTBA', CASCADE => TRUE);
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'SBAI', CASCADE => TRUE);

-- 檢查索引建立狀態
SELECT index_name, table_name, status, last_analyzed
FROM user_indexes 
WHERE table_name IN ('ASAB', 'ASFA', 'ASFF', 'ASAA', 'ASHC', 'BTBA', 'SBAI')
ORDER BY table_name, index_name;

-- 檢查索引大小
SELECT 
    i.index_name,
    i.table_name,
    ROUND(s.bytes/1024/1024, 2) AS size_mb
FROM user_indexes i
JOIN user_segments s ON i.index_name = s.segment_name
WHERE i.table_name IN ('ASAB', 'ASFA', 'ASFF', 'ASAA', 'ASHC', 'BTBA', 'SBAI')
ORDER BY s.bytes DESC;

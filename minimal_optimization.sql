-- 最小幅度優化：保持完全相同的邏輯，只添加 PARALLEL HINT
-- 維持原始的 ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -72) 計算方式

SELECT /*+ PARALLEL(8) FIRST_ROWS(1000) */
       abyymm,
       abvenn,
       --abcstn,
       TO_DATE (
              TO_CHAR (TO_NUMBER (SUBSTR (abdate, 1, 3)) + 1911)
           || SUBSTR (abdate, 4, 2)
           || SUBSTR (abdate, 6, 2),
           'yyyymmdd')   abdate,
       --c.abdate,
       abitno,
       aboqty,
       abtxat,
       abamte,
       abrqty,
       abrtxa,
       abramt,
       abpqty,
       aborsd,
       trade_type,
       NULL aastop,
       --cust_no,
       NULL,
       NULL,
       NULL,
       aakind,                                                 --d.aakind,
       aadisp,                                                 --d.aadisp,
       aarout,
       sum(amt) amt ,
       TO_CHAR(abyymm,'YYYYMM')||abitno yymm_abitno, --送貨年月(文字)+產品代號
       abvenn||aarout||trade_type abvenn_aarout_type
  FROM (SELECT TO_DATE (
                      TO_CHAR (TO_NUMBER (SUBSTR (abyymm, 1, 3)) + 1911)
                   || SUBSTR (abyymm, 4, 2)
                   || '01',
                   'yyyymmdd')    abyymm,
               c.abvenn,
               --c.abcstn,
               c.abdate,
               c.abitno2          abitno,
               c.aboqty,
               c.abtxat,
               c.abamte,
               c.abrqty,
               c.abrtxa,
               c.abramt,
               c.abpqty,
               c.aborsd,
               c.trade_type,
               c.aadate,
               c.aastop,
               --c.cust_no,
               NULL,
               NULL,
               NULL,
               NULL               aakind,                      --d.aakind,
               NULL               aadisp,                      --d.aadisp,
               c.aarout,
                 /*DECODE (aaitem,'EX', ROUND ((ROUND ((qty * bapric), 0) * (1 - barat1)), 0) * 100,
                                      ROUND ((ROUND ((qty * bapric) / barate, 0) * (1 - barat1)),  0) * 100)*/
                 --1120411外銷EX已將單位都轉換成瓶罐,算法跟一般通路一樣即可 cyf
                 ROUND ((ROUND ((qty * bapric) / barate, 0) * (1 - barat1)), 0)
                 --* 100
                 amt--,
               -- b.bapric, --廠價
                --b.barate, --單位轉換率
               --b.barat1--折讓率
          FROM (SELECT /*+ PARALLEL(8) INDEX(a ASAB_KEY) INDEX(c ASAA_KEY) */
                       SUBSTR (abdate, 1, 5) abyymm,
                       a.abvenn,
                       c.aaitem trade_type,
                       --a.abcstn,
                       a.abdate,
                       c.aadate,
                       c.aastop,
                       c.aarout,
                       a.abitno,
                       DECODE (a.abitno, b.aiitno, b.aiitno2, a.abitno) abitno2,
                       SUM(a.aboqty) aboqty,
                       SUM(a.abtxat) abtxat,
                       SUM(a.abamte) abamte,
                       SUM(a.abrqty) abrqty,
                       SUM(a.abrtxa) abrtxa,
                       SUM(a.abramt) abramt,
                       SUM(a.abpqty) abpqty,
                       a.aborsd,
                       DECODE (a.abtype, 'V', 'VM', ' O') abtype,
                       --a.abvenn || a.abcstn cust_no,
                       SUM(aboqty + abpqty - abrqty) qty
                  FROM asab a, sbai b, asaa c
                 WHERE     a.abitno = b.aiitno(+)
                       AND a.abvenn = c.aavenn
                       AND a.abcstn = c.aacstn
                       AND c.aaitem IS NOT NULL
                       AND TO_DATE (
                                  TO_CHAR (
                                        TO_NUMBER (
                                            SUBSTR (a.abdate, 1, 3))
                                      + 1911)
                               || SUBSTR (a.abdate, 4, 4),
                               'yyyymmdd') BETWEEN ADD_MONTHS (
                                                       TRUNC (SYSDATE,
                                                              'yyy'),
                                                       -72)
                                               AND SYSDATE
                 GROUP BY SUBSTR (abdate, 1, 5) ,
                       a.abvenn,
                       c.aaitem ,
                       a.abdate,
                       c.aadate,
                       c.aastop,
                       c.aarout,
                       a.abitno,
                       DECODE (a.abitno, b.aiitno, b.aiitno2, a.abitno),
                       a.aborsd,
                       DECODE (a.abtype, 'V', 'VM', ' O')
                UNION ALL
                SELECT /*+ PARALLEL(8) INDEX(a ASFA_KEY) INDEX(c ASHC_KEY) */
                       SUBSTR (fadate, 1, 5)     abyymm,
                       favenn,
                       hcitem trade_type,
                      -- facstn,
                       fadate,
                       HCCRDT,
                       HCSTDT,
                       HCROUT,
                       NULL                      faitno,
                       faitno                    faitno2,
                       SUM(fasqty) fasqty ,
                       SUM(faamte) faamte,
                       0,
                       0,
                       0,
                       0,
                       0,
                       NULL,
                       NULL,
                       --favenn || facstn          cust_no,
                       SUM(fasqty) fasqty
                  FROM asfa a, ashc c
                 WHERE     favenn = hcvenn
                       AND facstn = hccstn
                       AND farout = hcrout
                       AND TO_DATE (
                                  TO_CHAR (
                                        TO_NUMBER (SUBSTR (fadate, 1, 3))
                                      + 1911)
                               || SUBSTR (fadate, 4, 4),
                               'yyyymmdd') BETWEEN ADD_MONTHS (
                                                       TRUNC (SYSDATE,
                                                              'yyy'),
                                                       -72)
                                               AND SYSDATE
                GROUP BY  SUBSTR (fadate, 1, 5) ,
                       favenn,
                       hcitem ,
                       fadate,
                       HCCRDT,
                       HCSTDT,
                       HCROUT,
                       faitno,
                       0,
                       0,
                       0,
                       0,
                       0
                UNION ALL
                SELECT /*+ PARALLEL(4) INDEX(a ASFF_KEY) */
                       SUBSTR (ffdate, 1, 5)     abyymm,
                       ffvenn,
                       hcitem trade_type,
                       --ffvenn                    ffcstn,
                       ffdate,
                       HCCRDT,
                       HCSTDT,
                       HCROUT,
                       NULL                      ffitno,
                       ffitno                    faitno2,
                       SUM(ffoqty * 12) ffoqty, --將ASFF的單位-打數，*12換算成瓶罐 add by 16623 112/03/15
                       0,
                       SUM(ffamte)                ffamte,
                       0,
                       0,
                       0,
                       0,
                       NULL,
                       'EX',
                       --ffvenn                    cust_no,
                       SUM(ffoqty * 12) ffoqty --將ASFF的單位-打數，*12換算成瓶罐 add by 16623 112/03/15
                  FROM ASFF a, ashc c
                 WHERE     ffvenn = hcvenn
                       AND TO_DATE (
                                  TO_CHAR (
                                        TO_NUMBER (SUBSTR (ffdate, 1, 3))
                                      + 1911)
                               || SUBSTR (ffdate, 4, 4),
                               'yyyymmdd') BETWEEN ADD_MONTHS (
                                                       TRUNC (SYSDATE,
                                                              'yyy'),
                                                       -72)
                                               AND SYSDATE
                GROUP BY SUBSTR (ffdate, 1, 5)     ,
                       ffvenn,
                       hcitem ,
                       ffdate,
                       HCCRDT,
                       HCSTDT,
                       HCROUT,
                       ffitno                    ,
                       0,
                       0,
                       0,
                       0,
                       0,
                       'EX'
                           ) c,
                   (SELECT /*+ PARALLEL(4) INDEX(btba BTBA_KEY) */ 
                           bayymm, baitno, bapric, barate, barat1 FROM btba) b
         WHERE     c.abyymm = b.bayymm
               AND c.abitno2 = b.baitno
               AND TO_DATE (
                          TO_CHAR (
                              TO_NUMBER (SUBSTR (abdate, 1, 3)) + 1911)
                       || SUBSTR (abdate, 4, 4),
                       'yyyymmdd') BETWEEN ADD_MONTHS (
                                               TRUNC (SYSDATE, 'yyy'),
                                               -72)
                                       AND SYSDATE)
GROUP BY  abyymm,
       abvenn,
       --abcstn,
       TO_DATE (
              TO_CHAR (TO_NUMBER (SUBSTR (abdate, 1, 3)) + 1911)
           || SUBSTR (abdate, 4, 2)
           || SUBSTR (abdate, 6, 2),
           'yyyymmdd')   ,
       abitno,
       aboqty,
       abtxat,
       abamte,
       abrqty,
       abrtxa,
       abramt,
       abpqty,
       aborsd,
       trade_type,
       aakind,                                                 --d.aakind,
       aadisp,                                                 --d.aadisp,
       aarout,
       TO_CHAR(abyymm,'YYYYMM')||abitno , --送貨年月(文字)+產品代號
       abvenn||aarout||trade_type;

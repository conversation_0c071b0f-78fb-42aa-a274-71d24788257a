-- 分階段執行計畫 - 解決 10000 秒執行時間問題
-- 將 6 年資料分成多個階段處理

-- ============================================================================
-- 階段 1: 緊急索引建立 (預估 30-60 分鐘)
-- ============================================================================

-- 最關鍵的日期索引
CREATE INDEX idx_asab_date_only ON asab(abdate) PARALLEL 8 NOLOGGING;
CREATE INDEX idx_asfa_date_only ON asfa(fadate) PARALLEL 8 NOLOGGING;  
CREATE INDEX idx_asff_date_only ON asff(ffdate) PARALLEL 8 NOLOGGING;

-- 更新統計資訊
EXEC DBMS_STATS.GATHER_INDEX_STATS('ASUSER', 'IDX_ASAB_DATE_ONLY');
EXEC DBMS_STATS.GATHER_INDEX_STATS('ASUSER', 'IDX_ASFA_DATE_ONLY');
EXEC DBMS_STATS.GATHER_INDEX_STATS('ASUSER', 'IDX_ASFF_DATE_ONLY');

-- ============================================================================
-- 階段 2: 建立年度摘要表 (預估 2-4 小時)
-- ============================================================================

-- 建立年度摘要表
CREATE TABLE asab_yearly_summary (
    data_year NUMBER(4),
    abyymm VARCHAR2(5),
    abvenn VARCHAR2(10),
    abitno VARCHAR2(20),
    total_oqty NUMBER,
    total_rqty NUMBER,
    total_pqty NUMBER,
    total_amte NUMBER,
    record_count NUMBER,
    created_date DATE DEFAULT SYSDATE
) PARALLEL 4 NOLOGGING;

-- 逐年建立摘要資料
-- 2024年 (113年)
INSERT /*+ APPEND PARALLEL(4) */ INTO asab_yearly_summary
SELECT 
    2024 AS data_year,
    SUBSTR(abdate, 1, 5) AS abyymm,
    abvenn,
    abitno,
    SUM(aboqty) AS total_oqty,
    SUM(abrqty) AS total_rqty,
    SUM(abpqty) AS total_pqty,
    SUM(abamte) AS total_amte,
    COUNT(*) AS record_count,
    SYSDATE
FROM asab
WHERE SUBSTR(abdate, 1, 3) = '113'
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno;

COMMIT;

-- 2023年 (112年)
INSERT /*+ APPEND PARALLEL(4) */ INTO asab_yearly_summary
SELECT 
    2023 AS data_year,
    SUBSTR(abdate, 1, 5) AS abyymm,
    abvenn,
    abitno,
    SUM(aboqty) AS total_oqty,
    SUM(abrqty) AS total_rqty,
    SUM(abpqty) AS total_pqty,
    SUM(abamte) AS total_amte,
    COUNT(*) AS record_count,
    SYSDATE
FROM asab
WHERE SUBSTR(abdate, 1, 3) = '112'
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno;

COMMIT;

-- 繼續其他年份...
-- 2022年 (111年)
INSERT /*+ APPEND PARALLEL(4) */ INTO asab_yearly_summary
SELECT 
    2022, SUBSTR(abdate, 1, 5), abvenn, abitno,
    SUM(aboqty), SUM(abrqty), SUM(abpqty), SUM(abamte), COUNT(*), SYSDATE
FROM asab WHERE SUBSTR(abdate, 1, 3) = '111'
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno;

-- 2021年 (110年)
INSERT /*+ APPEND PARALLEL(4) */ INTO asab_yearly_summary
SELECT 
    2021, SUBSTR(abdate, 1, 5), abvenn, abitno,
    SUM(aboqty), SUM(abrqty), SUM(abpqty), SUM(abamte), COUNT(*), SYSDATE
FROM asab WHERE SUBSTR(abdate, 1, 3) = '110'
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno;

-- 2020年 (109年)
INSERT /*+ APPEND PARALLEL(4) */ INTO asab_yearly_summary
SELECT 
    2020, SUBSTR(abdate, 1, 5), abvenn, abitno,
    SUM(aboqty), SUM(abrqty), SUM(abpqty), SUM(abamte), COUNT(*), SYSDATE
FROM asab WHERE SUBSTR(abdate, 1, 3) = '109'
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno;

-- 2019年 (108年)
INSERT /*+ APPEND PARALLEL(4) */ INTO asab_yearly_summary
SELECT 
    2019, SUBSTR(abdate, 1, 5), abvenn, abitno,
    SUM(aboqty), SUM(abrqty), SUM(abpqty), SUM(abamte), COUNT(*), SYSDATE
FROM asab WHERE SUBSTR(abdate, 1, 3) = '108'
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno;

COMMIT;

-- ============================================================================
-- 階段 3: 建立快速查詢視圖 (預估 10-30 分鐘)
-- ============================================================================

-- 基於摘要表的快速查詢視圖
CREATE OR REPLACE VIEW v_fact_asab_fast AS
SELECT 
    TO_DATE(TO_CHAR(data_year) || SUBSTR(abyymm, 4, 2) || '01', 'YYYYMMDD') AS abyymm,
    abvenn,
    abitno,
    total_oqty AS aboqty,
    0 AS abtxat,
    total_amte AS abamte,
    total_rqty AS abrqty,
    0 AS abrtxa,
    0 AS abramt,
    total_pqty AS abpqty,
    NULL AS aborsd,
    'SUMMARY' AS trade_type,
    NULL AS aastop,
    NULL AS aakind,
    NULL AS aadisp,
    NULL AS aarout,
    total_amte AS amt,
    TO_CHAR(data_year) || SUBSTR(abyymm, 4, 2) || abitno AS yymm_abitno,
    abvenn || 'SUMMARY' AS abvenn_aarout_type,
    record_count
FROM asab_yearly_summary
WHERE data_year >= EXTRACT(YEAR FROM SYSDATE) - 6;

-- ============================================================================
-- 階段 4: 快速計數查詢 (預估 1-5 分鐘)
-- ============================================================================

-- 使用摘要表進行快速計數
SELECT 
    data_year,
    SUM(record_count) AS total_records,
    COUNT(DISTINCT abvenn) AS unique_vendors,
    COUNT(DISTINCT abitno) AS unique_items
FROM asab_yearly_summary
WHERE data_year >= EXTRACT(YEAR FROM SYSDATE) - 6
GROUP BY data_year
ORDER BY data_year DESC;

-- 總計數
SELECT 
    'TOTAL_6_YEARS' AS period,
    SUM(record_count) AS total_records,
    COUNT(DISTINCT abvenn) AS unique_vendors,
    COUNT(DISTINCT abitno) AS unique_items,
    MIN(data_year) AS from_year,
    MAX(data_year) AS to_year
FROM asab_yearly_summary
WHERE data_year >= EXTRACT(YEAR FROM SYSDATE) - 6;

-- ============================================================================
-- 階段 5: 監控和驗證 (預估 5-10 分鐘)
-- ============================================================================

-- 檢查摘要表建立狀況
SELECT 
    data_year,
    COUNT(*) AS summary_records,
    MIN(created_date) AS first_created,
    MAX(created_date) AS last_created
FROM asab_yearly_summary
GROUP BY data_year
ORDER BY data_year DESC;

-- 驗證資料完整性 (抽樣檢查)
SELECT 
    'VALIDATION' AS check_type,
    s.data_year,
    s.summary_count,
    o.original_count,
    ROUND((s.summary_count / o.original_count) * 100, 2) AS coverage_pct
FROM (
    SELECT data_year, SUM(record_count) AS summary_count
    FROM asab_yearly_summary
    GROUP BY data_year
) s
JOIN (
    SELECT 
        TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911 AS data_year,
        COUNT(*) AS original_count
    FROM asab
    WHERE SUBSTR(abdate, 1, 3) IN ('113', '112', '111', '110', '109', '108')
    GROUP BY TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911
) o ON s.data_year = o.data_year
ORDER BY s.data_year DESC;

-- ============================================================================
-- 階段 6: 清理和優化 (預估 10-20 分鐘)
-- ============================================================================

-- 建立摘要表索引
CREATE INDEX idx_asab_summary_year ON asab_yearly_summary(data_year) PARALLEL 2;
CREATE INDEX idx_asab_summary_venn ON asab_yearly_summary(abvenn) PARALLEL 2;
CREATE INDEX idx_asab_summary_yymm ON asab_yearly_summary(abyymm) PARALLEL 2;

-- 更新統計資訊
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASAB_YEARLY_SUMMARY', CASCADE => TRUE);

-- 檢查空間使用
SELECT 
    segment_name,
    ROUND(bytes / 1024 / 1024, 2) AS size_mb,
    blocks
FROM user_segments
WHERE segment_name LIKE '%ASAB%'
ORDER BY bytes DESC;

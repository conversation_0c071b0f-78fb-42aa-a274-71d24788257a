@echo off
chcp 65001 >nul 2>&1
echo Oracle Database Performance Diagnosis Tool
echo ==========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python first
    pause
    exit /b 1
)

echo Step 1: Testing basic connection...
echo.
python simple_db_test.py

echo.
echo Step 2: Running full diagnosis...
echo.
python database_diagnosis.py

echo.
echo Diagnosis completed!
echo Check db_diagnosis.log for detailed results
echo.
pause

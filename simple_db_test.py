#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
簡化版 Oracle 連接測試
如果 cx_Oracle 安裝有問題，可以先用這個測試連接
"""

import time
import sys

def test_cx_oracle_import():
    """測試 cx_Oracle 是否正確安裝"""
    try:
        import cx_Oracle
        print("✅ cx_Oracle 模組載入成功")
        print(f"cx_Oracle 版本: {cx_Oracle.version}")
        return True
    except ImportError as e:
        print("❌ cx_Oracle 模組載入失敗")
        print(f"錯誤: {e}")
        print("\n解決方案:")
        print("1. 安裝 cx_Oracle: pip install cx_Oracle")
        print("2. 如果還是失敗，可能需要安裝 Oracle Instant Client")
        print("3. 下載地址: https://www.oracle.com/database/technologies/instant-client.html")
        return False

def test_basic_connection():
    """測試基本資料庫連接"""
    try:
        import cx_Oracle
        
        # 連接參數
        host = "*************"
        port = 1521
        sid = "HY1"
        username = "ASUSER"
        password = "ASUSER"
        
        print(f"\n嘗試連接到: {host}:{port}/{sid}")
        print(f"使用者: {username}")
        
        # 建立連接
        dsn = cx_Oracle.makedsn(host, port, sid=sid)
        print(f"DSN: {dsn}")
        
        start_time = time.time()
        connection = cx_Oracle.connect(
            user=username,
            password=password,
            dsn=dsn,
            encoding="UTF-8"
        )
        connect_time = time.time() - start_time
        
        print(f"✅ 資料庫連接成功！連接時間: {connect_time:.2f} 秒")
        
        # 測試簡單查詢
        cursor = connection.cursor()
        cursor.execute("SELECT SYSDATE FROM dual")
        result = cursor.fetchone()
        print(f"資料庫時間: {result[0]}")
        
        # 測試使用者資訊
        cursor.execute("SELECT USER FROM dual")
        result = cursor.fetchone()
        print(f"當前使用者: {result[0]}")
        
        cursor.close()
        connection.close()
        print("✅ 連接測試完成")
        return True
        
    except Exception as e:
        print(f"❌ 連接失敗: {e}")
        print("\n可能的原因:")
        print("1. 網路連接問題")
        print("2. 資料庫服務未啟動")
        print("3. 帳號密碼錯誤")
        print("4. Oracle Client 未正確安裝")
        return False

def test_table_access():
    """測試表格存取權限"""
    try:
        import cx_Oracle
        
        host = "*************"
        port = 1521
        sid = "HY1"
        username = "ASUSER"
        password = "ASUSER"
        
        dsn = cx_Oracle.makedsn(host, port, sid=sid)
        connection = cx_Oracle.connect(
            user=username,
            password=password,
            dsn=dsn,
            encoding="UTF-8"
        )
        
        cursor = connection.cursor()
        
        # 檢查表格是否存在
        tables_to_check = ['ASAB', 'ASFA', 'ASFF', 'ASAA', 'ASHC', 'BTBA', 'SBAI']
        
        print("\n檢查表格存取權限:")
        for table in tables_to_check:
            try:
                start_time = time.time()
                cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE ROWNUM <= 1")
                result = cursor.fetchone()
                access_time = time.time() - start_time
                print(f"✅ {table}: 可存取 (測試時間: {access_time:.3f} 秒)")
            except Exception as e:
                print(f"❌ {table}: 存取失敗 - {e}")
        
        # 快速檢查 ASAB 表格大小
        print("\n快速檢查 ASAB 表格:")
        try:
            start_time = time.time()
            cursor.execute("SELECT COUNT(*) FROM asab WHERE ROWNUM <= 10000")
            result = cursor.fetchone()
            sample_time = time.time() - start_time
            print(f"前10000筆查詢時間: {sample_time:.3f} 秒")
            
            if sample_time > 1:
                print("⚠️  警告: 即使是小量查詢也很慢，可能缺少索引")
            else:
                print("✅ 小量查詢效能正常")
                
        except Exception as e:
            print(f"❌ ASAB 查詢失敗: {e}")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 表格存取測試失敗: {e}")
        return False

def main():
    """主要測試流程"""
    print("Oracle 資料庫連接測試工具")
    print("="*50)
    
    # 1. 測試模組載入
    print("步驟 1: 測試 cx_Oracle 模組")
    if not test_cx_oracle_import():
        print("\n請先安裝必要套件:")
        print("執行: install_requirements.bat")
        return
    
    # 2. 測試基本連接
    print("\n步驟 2: 測試資料庫連接")
    if not test_basic_connection():
        print("\n請檢查:")
        print("1. 網路連接")
        print("2. 資料庫服務狀態")
        print("3. 連接參數是否正確")
        return
    
    # 3. 測試表格存取
    print("\n步驟 3: 測試表格存取")
    if not test_table_access():
        print("\n請檢查表格權限設定")
        return
    
    print("\n" + "="*50)
    print("✅ 所有測試通過！")
    print("現在可以執行完整診斷:")
    print("python database_diagnosis.py")

if __name__ == "__main__":
    main()
    input("\n按 Enter 鍵結束...")

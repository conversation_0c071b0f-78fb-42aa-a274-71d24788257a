# Oracle 資料庫自動化效能診斷和修復工具

## 🎯 問題描述
- **原始問題**: SQL 查詢執行時間過長 (3年資料需要5000秒，6年資料預估10000秒)
- **資料量**: ASAB (42M筆)、ASFA (47M筆)、ASFF (12K筆)，總計約90M筆記錄
- **瓶頸**: 缺少索引、複雜日期轉換、大型表格全表掃描

## 🤖 解決方案
完全自動化的診斷和修復工具，無需手動干預，一鍵解決效能問題。

### 自動修復功能
- ✅ **自動建立關鍵索引** (日期欄位索引)
- ✅ **建立年度摘要表** (預先計算結果)
- ✅ **更新統計資訊** (優化執行計畫)
- ✅ **生成優化查詢** (替代原始慢查詢)
- ✅ **驗證改善效果** (測試新查詢效能)

## 📁 檔案說明

### 🚀 執行檔案
- **`auto_fix_performance.bat`** - 一鍵自動修復 (推薦使用)
- **`run_diagnosis.bat`** - 完整診斷流程
- **`install_requirements.bat`** - 安裝 Python 套件

### 🔧 核心工具
- **`database_diagnosis.py`** - 主要診斷和修復工具
- **`simple_db_test.py`** - 資料庫連接測試

### 📊 輸出檔案 (執行後生成)
- **`performance_report.txt`** - 詳細診斷報告
- **`optimized_queries_auto.sql`** - 優化後的查詢腳本
- **`db_diagnosis.log`** - 執行日誌

## 🚀 使用方法

### 方法一: 一鍵自動修復 (推薦)
```bash
# 雙擊執行
auto_fix_performance.bat
```

### 方法二: 分步執行
```bash
# 1. 安裝套件
install_requirements.bat

# 2. 測試連接
python simple_db_test.py

# 3. 執行診斷和修復
python database_diagnosis.py
```

## 📈 預期效能改善

| 項目 | 修復前 | 修復後 | 改善幅度 |
|------|--------|--------|----------|
| 6年資料查詢 | 10000秒 (2.8小時) | 10-30秒 | 99%+ |
| 3年資料查詢 | 5000秒 (1.4小時) | 5-15秒 | 99%+ |
| 簡單計數 | 數分鐘 | 數秒 | 95%+ |

## 🔧 技術細節

### 自動建立的索引
```sql
-- 關鍵日期索引
CREATE INDEX idx_asab_abdate_auto ON asab(abdate) PARALLEL 8 NOLOGGING;
CREATE INDEX idx_asfa_fadate_auto ON asfa(fadate) PARALLEL 8 NOLOGGING;
CREATE INDEX idx_asff_ffdate_auto ON asff(ffdate) PARALLEL 8 NOLOGGING;
```

### 自動建立的摘要表
```sql
-- 年度摘要表 (快速查詢)
CREATE TABLE asab_yearly_summary_auto (
    data_year NUMBER(4),
    abyymm VARCHAR2(5),
    abvenn VARCHAR2(10),
    abitno VARCHAR2(20),
    total_oqty NUMBER,
    total_rqty NUMBER,
    total_pqty NUMBER,
    total_amte NUMBER,
    record_count NUMBER,
    created_date DATE DEFAULT SYSDATE
);
```

### 優化查詢範例
```sql
-- 原始慢查詢 (10000秒)
SELECT COUNT(*) FROM asab 
WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd') 
      >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36);

-- 優化後快查詢 (10秒)
SELECT SUM(record_count) FROM asab_yearly_summary_auto
WHERE data_year >= EXTRACT(YEAR FROM SYSDATE) - 6;
```

## 🔍 診斷項目

### 自動檢查
- 📊 表格統計資訊 (記錄數、大小、最後分析時間)
- 🔍 索引狀況 (是否有日期索引)
- ⏱️ 效能測試 (1年、3年計數查詢)
- 📈 資料分布分析 (各年度資料量)

### 自動修復
- 🔧 建立缺失的關鍵索引
- 📊 建立年度摘要表
- 📈 更新統計資訊
- 🚀 生成優化查詢腳本

## ⚠️ 注意事項

### 執行前準備
- 確保有足夠的表格空間 (建立索引需要額外空間)
- 建議在非營業時間執行 (索引建立可能影響其他查詢)
- 確保有 ASUSER 的 CREATE INDEX 權限

### 執行時間預估
- 索引建立: 30-60分鐘 (視資料量而定)
- 摘要表建立: 10-30分鐘
- 總執行時間: 約1-2小時 (但只需執行一次)

### 風險評估
- **低風險**: 只建立索引和摘要表，不修改原始資料
- **可回復**: 如有問題可刪除建立的索引和表格
- **測試建議**: 可先在測試環境執行

## 🔄 後續維護

### 定期維護建議
- **每月**: 重建索引 (`ALTER INDEX ... REBUILD`)
- **每週**: 更新統計資訊 (`DBMS_STATS.GATHER_TABLE_STATS`)
- **每季**: 執行此診斷工具檢查效能

### 摘要表維護
```sql
-- 新增最新年度資料到摘要表
INSERT INTO asab_yearly_summary_auto
SELECT 2025, SUBSTR(abdate, 1, 5), abvenn, abitno,
       SUM(aboqty), SUM(abrqty), SUM(abpqty), SUM(abamte), 
       COUNT(*), SYSDATE
FROM asab 
WHERE SUBSTR(abdate, 1, 3) = '114'  -- 2025年 = 114年
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno;
```

## 📞 支援

如果遇到問題，請檢查：
1. `db_diagnosis.log` - 詳細執行日誌
2. `performance_report.txt` - 診斷報告
3. 資料庫連接是否正常
4. Python 套件是否正確安裝

## 🎉 成功指標

執行成功後，您應該看到：
- ✅ 索引建立成功訊息
- ✅ 摘要表建立完成
- ✅ 效能測試顯示大幅改善
- ✅ 生成優化查詢腳本

**預期結果**: 原本需要數小時的查詢，現在只需要數秒鐘！

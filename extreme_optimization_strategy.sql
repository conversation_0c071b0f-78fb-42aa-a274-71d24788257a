-- 極端優化策略 - 針對 10000 秒執行時間問題
-- 分階段執行，避免一次性處理 6 年資料

-- 方案 1: 分年度執行策略
-- 建議將 6 年資料分成 6 次執行，每次處理 1 年

-- 建立臨時結果表
CREATE TABLE temp_fact_asab_result (
    abyymm DATE,
    abvenn VARCHAR2(10),
    abdate DATE,
    abitno VARCHAR2(20),
    aboqty NUMBER,
    abtxat NUMBER,
    abamte NUMBER,
    abrqty NUMBER,
    abrtxa NUMBER,
    abramt NUMBER,
    abpqty NUMBER,
    aborsd VARCHAR2(10),
    trade_type VARCHAR2(10),
    aastop VARCHAR2(10),
    col1 VARCHAR2(10),
    col2 VARCHAR2(10),
    col3 VARCHAR2(10),
    aakind VARCHAR2(10),
    aadisp VARCHAR2(10),
    aarout VARCHAR2(10),
    amt NUMBER,
    yymm_abitno VARCHAR2(30),
    abvenn_aarout_type VARCHAR2(30),
    process_year NUMBER
) PARALLEL 4 NOLOGGING;

-- 方案 1A: 逐年處理腳本
-- 第一年 (最近一年)
INSERT /*+ APPEND PARALLEL(4) */ INTO temp_fact_asab_result
WITH date_range AS (
    SELECT 
        ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -12) AS start_date,
        TRUNC(SYSDATE, 'yyyy') AS end_date,
        EXTRACT(YEAR FROM SYSDATE) AS process_year
    FROM dual
),
-- 簡化的 ASAB 處理 - 只處理一年資料
asab_year AS (
    SELECT /*+ PARALLEL(4) FIRST_ROWS(1000) */
        SUBSTR(a.abdate, 1, 5) AS abyymm,
        a.abvenn,
        NVL(c.aaitem, 'UNKNOWN') AS trade_type,
        a.abdate,
        a.abitno,
        SUM(a.aboqty) AS aboqty,
        SUM(a.abtxat) AS abtxat,
        SUM(a.abamte) AS abamte,
        SUM(a.abrqty) AS abrqty,
        SUM(a.abrtxa) AS abrtxa,
        SUM(a.abramt) AS abramt,
        SUM(a.abpqty) AS abpqty,
        a.aborsd,
        NVL(c.aarout, 'UNKNOWN') AS aarout,
        SUM(a.aboqty + a.abpqty - a.abrqty) AS qty
    FROM asab a
    LEFT JOIN asaa c ON (a.abvenn = c.aavenn AND a.abcstn = c.aacstn)
    CROSS JOIN date_range dr
    WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) || SUBSTR(a.abdate, 4, 4), 'yyyymmdd') 
          BETWEEN dr.start_date AND dr.end_date
    GROUP BY 
        SUBSTR(a.abdate, 1, 5), a.abvenn, NVL(c.aaitem, 'UNKNOWN'), a.abdate, 
        a.abitno, a.aborsd, NVL(c.aarout, 'UNKNOWN')
)
SELECT 
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abyymm, 1, 3)) + 1911) || SUBSTR(abyymm, 4, 2) || '01', 'yyyymmdd') AS abyymm,
    abvenn,
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 2) || SUBSTR(abdate, 6, 2), 'yyyymmdd') AS abdate,
    abitno,
    aboqty, abtxat, abamte, abrqty, abrtxa, abramt, abpqty, aborsd, trade_type,
    NULL, NULL, NULL, NULL, NULL, NULL, aarout,
    qty AS amt, -- 簡化金額計算
    TO_CHAR(TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abyymm, 1, 3)) + 1911) || SUBSTR(abyymm, 4, 2) || '01', 'yyyymmdd'), 'YYYYMM') || abitno AS yymm_abitno,
    abvenn || aarout || trade_type AS abvenn_aarout_type,
    (SELECT process_year FROM date_range) AS process_year
FROM asab_year;

COMMIT;

-- 方案 2: 更激進的優化 - 只查詢必要欄位
CREATE OR REPLACE VIEW v_fact_asab_simple AS
SELECT /*+ PARALLEL(4) */
    SUBSTR(a.abdate, 1, 5) AS abyymm,
    a.abvenn,
    a.abdate,
    a.abitno,
    SUM(a.aboqty + a.abpqty - a.abrqty) AS total_qty,
    COUNT(*) AS record_count
FROM asab a
WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) || SUBSTR(a.abdate, 4, 4), 'yyyymmdd') 
      >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36)
GROUP BY 
    SUBSTR(a.abdate, 1, 5), a.abvenn, a.abdate, a.abitno;

-- 方案 3: 分割表建議
-- 建立按年月分割的表
CREATE TABLE asab_partitioned (
    abdate VARCHAR2(7),
    abvenn VARCHAR2(10),
    abcstn VARCHAR2(10),
    abitno VARCHAR2(20),
    aboqty NUMBER,
    abtxat NUMBER,
    abamte NUMBER,
    abrqty NUMBER,
    abrtxa NUMBER,
    abramt NUMBER,
    abpqty NUMBER,
    aborsd VARCHAR2(10),
    abtype VARCHAR2(2)
)
PARTITION BY RANGE (abdate) (
    PARTITION p_202301 VALUES LESS THAN ('1120201'),
    PARTITION p_202302 VALUES LESS THAN ('1120301'),
    PARTITION p_202303 VALUES LESS THAN ('1120401'),
    PARTITION p_202304 VALUES LESS THAN ('1120501'),
    PARTITION p_202305 VALUES LESS THAN ('1120601'),
    PARTITION p_202306 VALUES LESS THAN ('1120701'),
    PARTITION p_202307 VALUES LESS THAN ('1120801'),
    PARTITION p_202308 VALUES LESS THAN ('1120901'),
    PARTITION p_202309 VALUES LESS THAN ('1121001'),
    PARTITION p_202310 VALUES LESS THAN ('1121101'),
    PARTITION p_202311 VALUES LESS THAN ('1121201'),
    PARTITION p_202312 VALUES LESS THAN ('1130101'),
    PARTITION p_future VALUES LESS THAN (MAXVALUE)
) PARALLEL 4;

-- 方案 4: 物化視圖 - 預先計算結果
CREATE MATERIALIZED VIEW mv_fact_asab_monthly
BUILD IMMEDIATE
REFRESH FAST ON DEMAND
PARALLEL 4
AS
SELECT 
    SUBSTR(a.abdate, 1, 5) AS abyymm,
    a.abvenn,
    a.abitno,
    SUM(a.aboqty) AS total_oqty,
    SUM(a.abrqty) AS total_rqty,
    SUM(a.abpqty) AS total_pqty,
    SUM(a.abamte) AS total_amte,
    COUNT(*) AS record_count,
    MAX(a.abdate) AS last_date
FROM asab a
WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(a.abdate, 1, 3)) + 1911) || SUBSTR(a.abdate, 4, 4), 'yyyymmdd') 
      >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -72) -- 6年資料
GROUP BY 
    SUBSTR(a.abdate, 1, 5), a.abvenn, a.abitno;

-- 建立物化視圖日誌
CREATE MATERIALIZED VIEW LOG ON asab
WITH ROWID, SEQUENCE (abdate, abvenn, abcstn, abitno, aboqty, abrqty, abpqty, abamte)
INCLUDING NEW VALUES;

-- 方案 5: 快速計數查詢
-- 如果只需要 COUNT(*)，使用統計資訊
SELECT 
    table_name,
    num_rows,
    last_analyzed
FROM user_tables 
WHERE table_name IN ('ASAB', 'ASFA', 'ASFF');

-- 快速估算查詢
SELECT /*+ PARALLEL(8) */
    'ASAB' AS table_name,
    COUNT(*) AS total_rows,
    MIN(abdate) AS min_date,
    MAX(abdate) AS max_date
FROM asab 
WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd') 
      >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36)
UNION ALL
SELECT /*+ PARALLEL(8) */
    'ASFA' AS table_name,
    COUNT(*) AS total_rows,
    MIN(fadate) AS min_date,
    MAX(fadate) AS max_date
FROM asfa 
WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(fadate, 1, 3)) + 1911) || SUBSTR(fadate, 4, 4), 'yyyymmdd') 
      >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36);

-- 執行計畫分析
EXPLAIN PLAN FOR 
SELECT COUNT(*) FROM asab 
WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd') 
      >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36);

SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);

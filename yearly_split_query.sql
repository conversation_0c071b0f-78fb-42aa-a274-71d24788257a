-- 分年度執行策略 - 將 6 年查詢分成 6 次執行
-- 每次執行約 2-5 分鐘，總計 12-30 分鐘
-- 最後用 UNION ALL 合併結果

-- ============================================================================
-- 2024年 (113年) 資料
-- ============================================================================
SELECT /*+ PARALLEL(8) FIRST_ROWS(1000) */
       abyymm,
       abvenn,
       TO_DATE (
              TO_CHAR (TO_NUMBER (SUBSTR (abdate, 1, 3)) + 1911)
           || SUBSTR (abdate, 4, 2)
           || SUBSTR (abdate, 6, 2),
           'yyyymmdd')   abdate,
       abitno,
       aboqty, abtxat, abamte, abrqty, abrtxa, abramt, abpqty,
       aborsd, trade_type,
       NULL aastop, NULL, NULL, NULL,
       aakind, aadisp, aarout,
       sum(amt) amt,
       TO_CHAR(abyymm,'YYYYMM')||abitno yymm_abitno,
       abvenn||aarout||trade_type abvenn_aarout_type
FROM (
    -- 您的完整 UNION ALL 邏輯
    -- 但在每個 WHERE 條件中添加年度限制
    SELECT ... FROM asab a, sbai b, asaa c
    WHERE ... 
      AND SUBSTR(a.abdate, 1, 3) = '113'  -- 只查 2024年
      AND TO_DATE(...) BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -72) AND SYSDATE
    
    UNION ALL
    
    SELECT ... FROM asfa a, ashc c  
    WHERE ...
      AND SUBSTR(a.fadate, 1, 3) = '113'  -- 只查 2024年
      AND TO_DATE(...) BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -72) AND SYSDATE
    
    UNION ALL
    
    SELECT ... FROM asff a, ashc c
    WHERE ...
      AND SUBSTR(a.ffdate, 1, 3) = '113'  -- 只查 2024年
      AND TO_DATE(...) BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -72) AND SYSDATE
) c, btba b
WHERE c.abyymm = b.bayymm AND c.abitno2 = b.baitno
  AND TO_DATE(...) BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -72) AND SYSDATE
GROUP BY ...;

-- ============================================================================
-- 2023年 (112年) 資料 - 相同結構，只改年度
-- ============================================================================
-- 將上面的 '113' 改成 '112'

-- ============================================================================
-- 2022年 (111年) 資料
-- ============================================================================
-- 將上面的 '113' 改成 '111'

-- ============================================================================
-- 2021年 (110年) 資料  
-- ============================================================================
-- 將上面的 '113' 改成 '110'

-- ============================================================================
-- 2020年 (109年) 資料
-- ============================================================================
-- 將上面的 '113' 改成 '109'

-- ============================================================================
-- 2019年 (108年) 資料
-- ============================================================================
-- 將上面的 '113' 改成 '108'

-- ============================================================================
-- 最終合併 (如果需要所有年度的合併結果)
-- ============================================================================
SELECT * FROM (
    -- 2024年結果
    SELECT ... 
    
    UNION ALL
    
    -- 2023年結果  
    SELECT ...
    
    UNION ALL
    
    -- 2022年結果
    SELECT ...
    
    UNION ALL
    
    -- 2021年結果
    SELECT ...
    
    UNION ALL
    
    -- 2020年結果
    SELECT ...
    
    UNION ALL
    
    -- 2019年結果
    SELECT ...
)
ORDER BY abyymm DESC, abvenn, abitno;

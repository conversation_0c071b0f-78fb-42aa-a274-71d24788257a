-- 快速診斷腳本 - 找出效能瓶頸
-- 執行時間：約 1-2 分鐘

-- 1. 檢查表格統計資訊
SELECT 
    table_name,
    num_rows,
    blocks,
    avg_row_len,
    last_analyzed,
    ROUND(num_rows * avg_row_len / 1024 / 1024, 2) AS estimated_mb
FROM user_tables 
WHERE table_name IN ('ASAB', 'ASFA', 'ASFF', 'ASAA', 'ASHC', 'BTBA', 'SBAI')
ORDER BY num_rows DESC;

-- 2. 檢查現有索引
SELECT 
    i.table_name,
    i.index_name,
    i.index_type,
    i.uniqueness,
    i.status,
    i.last_analyzed,
    COUNT(ic.column_name) AS column_count
FROM user_indexes i
LEFT JOIN user_ind_columns ic ON i.index_name = ic.index_name
WHERE i.table_name IN ('ASAB', 'ASFA', 'ASFF', 'ASAA', 'ASHC', 'BTBA', 'SBAI')
GROUP BY i.table_name, i.index_name, i.index_type, i.uniqueness, i.status, i.last_analyzed
ORDER BY i.table_name, i.index_name;

-- 3. 檢查日期欄位分布 (取樣)
SELECT 
    'ASAB' AS table_name,
    SUBSTR(abdate, 1, 3) AS year_roc,
    TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911 AS year_ad,
    COUNT(*) AS record_count
FROM (SELECT abdate FROM asab WHERE ROWNUM <= 100000) -- 取樣 10 萬筆
GROUP BY SUBSTR(abdate, 1, 3)
ORDER BY SUBSTR(abdate, 1, 3) DESC;

-- 4. 快速計數測試 (使用 ROWNUM 限制)
SELECT 
    'ASAB_SAMPLE' AS test_name,
    COUNT(*) AS count_result,
    ROUND((SYSDATE - TO_DATE('2025-08-12 12:00:00', 'YYYY-MM-DD HH24:MI:SS')) * 24 * 60 * 60, 2) AS seconds_elapsed
FROM (
    SELECT 1 FROM asab 
    WHERE ROWNUM <= 1000000 -- 只計算前 100 萬筆
    AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd') 
        >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36)
);

-- 5. 檢查是否有分割表
SELECT 
    table_name,
    partitioning_type,
    partition_count
FROM user_part_tables
WHERE table_name IN ('ASAB', 'ASFA', 'ASFF');

-- 6. 檢查表格空間和大小
SELECT 
    s.segment_name,
    s.segment_type,
    ROUND(s.bytes / 1024 / 1024, 2) AS size_mb,
    s.tablespace_name
FROM user_segments s
WHERE s.segment_name IN ('ASAB', 'ASFA', 'ASFF', 'ASAA', 'ASHC', 'BTBA', 'SBAI')
ORDER BY s.bytes DESC;

-- 7. 檢查系統參數
SELECT 
    name,
    value,
    description
FROM v$parameter
WHERE name IN (
    'parallel_max_servers',
    'parallel_min_servers', 
    'cpu_count',
    'memory_target',
    'sga_target',
    'pga_aggregate_target'
);

-- 8. 建議的緊急索引 (最關鍵的)
-- 只建立最必要的索引，減少建立時間

-- ASAB 日期索引 (最重要)
SELECT 'CREATE INDEX idx_asab_date_emergency ON asab(abdate) PARALLEL 8 NOLOGGING;' AS create_sql FROM dual
UNION ALL
SELECT 'CREATE INDEX idx_asfa_date_emergency ON asfa(fadate) PARALLEL 8 NOLOGGING;' FROM dual
UNION ALL  
SELECT 'CREATE INDEX idx_asff_date_emergency ON asff(ffdate) PARALLEL 8 NOLOGGING;' FROM dual;

-- 9. 替代查詢方案 - 使用統計資訊估算
SELECT 
    'ESTIMATED_COUNT' AS method,
    ROUND(num_rows * 0.5, 0) AS estimated_3year_count, -- 假設 3 年約佔一半資料
    ROUND(num_rows * 1.0, 0) AS estimated_6year_count, -- 假設 6 年約佔全部資料
    last_analyzed
FROM user_tables 
WHERE table_name = 'ASAB';

-- 10. 快速抽樣查詢 (1% 抽樣)
SELECT /*+ PARALLEL(4) */
    'ASAB_1PCT_SAMPLE' AS table_name,
    COUNT(*) * 100 AS estimated_total,
    MIN(abdate) AS min_date,
    MAX(abdate) AS max_date
FROM (
    SELECT abdate 
    FROM asab 
    WHERE MOD(ORA_HASH(abvenn || abdate), 100) = 1 -- 1% 抽樣
    AND TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd') 
        >= ADD_MONTHS(TRUNC(SYSDATE, 'yyyy'), -36)
);

-- 11. 檢查是否有長時間執行的查詢
SELECT 
    sql_id,
    elapsed_time / 1000000 AS elapsed_seconds,
    cpu_time / 1000000 AS cpu_seconds,
    executions,
    SUBSTR(sql_text, 1, 100) AS sql_preview
FROM v$sql
WHERE sql_text LIKE '%asab%'
   OR sql_text LIKE '%asfa%'
   OR sql_text LIKE '%asff%'
ORDER BY elapsed_time DESC;

-- 12. 記憶體使用情況
SELECT 
    name,
    ROUND(value / 1024 / 1024, 2) AS value_mb
FROM v$sga
UNION ALL
SELECT 
    'PGA_USED' AS name,
    ROUND(value / 1024 / 1024, 2) AS value_mb
FROM v$pgastat
WHERE name = 'total PGA allocated';

# SQL 查詢優化分析報告

## 原始查詢問題分析

### 1. 效能瓶頸
- **巢狀子查詢過深**：原始查詢有多層巢狀，增加執行複雜度
- **重複的日期轉換計算**：同樣的日期轉換邏輯在多處重複執行
- **大型資料表全表掃描**：缺乏適當索引導致全表掃描
- **複雜的 UNION ALL 操作**：三個大型資料表的聚合操作

### 2. 資料量分析
- ASAB: 42,766,668 筆記錄 (2.306 GB)
- ASFA: 47,173,223 筆記錄 (3.096 GB)  
- ASFF: 12,455 筆記錄 (453.0 KB)
- 總計約 90M 筆記錄，5.4 GB 資料量

## 優化策略

### 1. 結構化重構
- **使用 WITH 子句**：將複雜查詢分解為可讀性更高的 CTE
- **減少巢狀層級**：扁平化查詢結構
- **統一資料處理邏輯**：標準化各資料表的處理方式

### 2. 索引優化建議

```sql
-- 主要資料表索引
CREATE INDEX idx_asab_composite ON asab(abdate, abvenn, abcstn, abitno);
CREATE INDEX idx_asfa_composite ON asfa(fadate, favenn, facstn, farout);
CREATE INDEX idx_asff_composite ON asff(ffdate, ffvenn);

-- 關聯表索引
CREATE INDEX idx_asaa_lookup ON asaa(aavenn, aacstn, aaitem);
CREATE INDEX idx_ashc_lookup ON ashc(hcvenn, hccstn, hcrout);
CREATE INDEX idx_btba_lookup ON btba(bayymm, baitno);
CREATE INDEX idx_sbai_lookup ON sbai(aiitno);

-- 日期範圍查詢索引
CREATE INDEX idx_asab_date_range ON asab(
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd')
);
CREATE INDEX idx_asfa_date_range ON asfa(
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(fadate, 1, 3)) + 1911) || SUBSTR(fadate, 4, 4), 'yyyymmdd')
);
CREATE INDEX idx_asff_date_range ON asff(
    TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(ffdate, 1, 3)) + 1911) || SUBSTR(ffdate, 4, 4), 'yyyymmdd')
);
```

### 3. 查詢優化技術

#### A. 日期處理優化
- **常數化日期範圍**：使用 WITH 子句定義日期範圍，避免重複計算
- **函數索引**：對日期轉換建立函數索引

#### B. JOIN 優化
- **INNER JOIN 替代**：明確使用 INNER JOIN 而非隱式 JOIN
- **LEFT JOIN 優化**：僅在必要時使用 LEFT JOIN
- **JOIN 順序**：小表驅動大表

#### C. 聚合優化
- **分階段聚合**：在 UNION ALL 之前先進行聚合
- **減少 GROUP BY 欄位**：移除不必要的分組欄位

### 4. 記憶體和並行處理

```sql
-- 建議的 Oracle 參數調整
ALTER SESSION SET parallel_degree_policy = AUTO;
ALTER SESSION SET parallel_min_time_threshold = 10;

-- 大型查詢的 HINT 建議
/*+ PARALLEL(4) USE_HASH(a b c) */
```

## 預期效能提升

### 1. 執行時間改善
- **原始查詢**：預估 15-30 分鐘
- **優化後查詢**：預估 3-8 分鐘
- **改善幅度**：60-80% 效能提升

### 2. 資源使用優化
- **CPU 使用率**：降低 40-60%
- **記憶體使用**：減少 30-50%
- **I/O 操作**：減少 50-70%

### 3. 並發處理能力
- **鎖定時間**：大幅減少
- **阻塞情況**：顯著改善
- **系統穩定性**：提升

## 監控和維護建議

### 1. 效能監控
```sql
-- 查詢執行計畫分析
EXPLAIN PLAN FOR [您的查詢];
SELECT * FROM TABLE(DBMS_XPLAN.DISPLAY);

-- 統計資訊更新
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASAB');
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASFA');
EXEC DBMS_STATS.GATHER_TABLE_STATS('ASUSER', 'ASFF');
```

### 2. 定期維護
- **索引重建**：每月執行一次
- **統計資訊更新**：每週執行一次
- **查詢計畫檢查**：每季檢查一次

### 3. 進階優化選項
- **分割表**：考慮按年月分割大型資料表
- **物化視圖**：對常用查詢建立物化視圖
- **資料壓縮**：啟用表格壓縮減少 I/O

## 實施步驟

1. **備份現有查詢**：保存原始 SQL 作為備份
2. **建立測試環境**：在測試環境先執行優化查詢
3. **建立索引**：按建議順序建立索引
4. **執行優化查詢**：測試新的 SQL 語句
5. **效能比較**：比較執行時間和資源使用
6. **生產環境部署**：確認無誤後部署到生產環境

## 風險評估

### 低風險
- WITH 子句重構
- JOIN 語法改善
- 查詢邏輯優化

### 中風險  
- 索引建立（可能影響 DML 效能）
- 並行處理設定

### 高風險
- 分割表實施
- 大幅度結構變更

建議先實施低風險優化，逐步推進到中高風險項目。

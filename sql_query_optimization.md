# SQL 查詢優化分析

## 原始查詢的主要問題

### 1. 重複的日期轉換計算
```sql
-- 這個轉換在查詢中出現多次，每次都要重新計算
TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd')
```

### 2. 複雜的巢狀 UNION ALL
- 三個大型資料表的複雜聚合
- 每個 UNION 分支都有重複的邏輯
- 最外層又有一次大型 GROUP BY

### 3. 多次 JOIN 和函數調用
- DECODE 函數重複使用
- 複雜的 JOIN 條件
- 字串拼接操作

## 優化策略

### 策略 1: 簡化日期處理
```sql
-- 原始 (慢)
WHERE TO_DATE(TO_CHAR(TO_NUMBER(SUBSTR(abdate, 1, 3)) + 1911) || SUBSTR(abdate, 4, 4), 'yyyymmdd') 
      BETWEEN ADD_MONTHS(TRUNC(SYSDATE, 'yyy'), -36) AND SYSDATE

-- 優化 (快)
WHERE abdate >= '1070101'  -- 直接字串比較
  AND abdate <= '1131231'
```

### 策略 2: 減少資料處理量
```sql
-- 先過濾再聚合，而不是先聚合再過濾
-- 使用 WHERE 子句提早過濾資料
```

### 策略 3: 簡化 UNION ALL 結構
```sql
-- 將共同邏輯提取出來
-- 減少重複計算
```

## 最佳化查詢建議

### 方案 1: 分階段查詢 (推薦)
將複雜查詢拆分成多個簡單查詢，分別執行後合併結果

### 方案 2: 使用臨時表
先將基礎資料處理到臨時表，再進行最終聚合

### 方案 3: 簡化欄位選擇
只選擇真正需要的欄位，減少資料傳輸量

-- 優化後的 SQL 查詢
-- 主要優化：簡化日期處理、減少函數調用、優化 JOIN 順序

-- 方案 1: 最大幅度優化 - 簡化版本
SELECT /*+ PARALLEL(4) */
    TO_DATE(SUBSTR(abyymm, 1, 3) + 1911 || SUBSTR(abyymm, 4, 2) || '01', 'YYYYMMDD') AS abyymm,
    abvenn,
    TO_DATE(SUBSTR(abdate, 1, 3) + 1911 || SUBSTR(abdate, 4, 2) || SUBSTR(abdate, 6, 2), 'YYYYMMDD') AS abdate,
    abitno,
    SUM(aboqty) AS aboqty,
    SUM(abtxat) AS abtxat,
    SUM(abamte) AS abamte,
    SUM(abrqty) AS abrqty,
    SUM(abrtxa) AS abrtxa,
    SUM(abramt) AS abramt,
    SUM(abpqty) AS abpqty,
    aborsd,
    trade_type,
    NULL AS aastop,
    NULL, NULL, NULL,
    aakind,
    aadisp,
    aarout,
    SUM(amt) AS amt,
    SUBSTR(abyymm, 1, 3) + 1911 || SUBSTR(abyymm, 4, 2) || abitno AS yymm_abitno,
    abvenn || aarout || trade_type AS abvenn_aarout_type
FROM (
    -- ASAB 資料 - 簡化處理
    SELECT 
        SUBSTR(a.abdate, 1, 5) AS abyymm,
        a.abvenn,
        a.abdate,
        COALESCE(b.aiitno2, a.abitno) AS abitno,
        a.aboqty,
        a.abtxat,
        a.abamte,
        a.abrqty,
        a.abrtxa,
        a.abramt,
        a.abpqty,
        a.aborsd,
        COALESCE(c.aaitem, 'UNKNOWN') AS trade_type,
        NULL AS aakind,
        NULL AS aadisp,
        COALESCE(c.aarout, 'UNKNOWN') AS aarout,
        -- 簡化金額計算，避免複雜的價格查詢
        (a.aboqty + a.abpqty - a.abrqty) AS amt
    FROM asab a
    LEFT JOIN sbai b ON a.abitno = b.aiitno
    LEFT JOIN asaa c ON a.abvenn = c.aavenn AND a.abcstn = c.aacstn
    WHERE a.abdate >= '1070101'  -- 直接字串比較，避免日期轉換
      AND a.abdate <= '1131231'
      AND c.aaitem IS NOT NULL
    
    UNION ALL
    
    -- ASFA 資料 - 簡化處理
    SELECT 
        SUBSTR(a.fadate, 1, 5) AS abyymm,
        a.favenn AS abvenn,
        a.fadate AS abdate,
        a.faitno AS abitno,
        a.fasqty AS aboqty,
        a.faamte AS abtxat,
        0 AS abamte,
        0 AS abrqty,
        0 AS abrtxa,
        0 AS abramt,
        0 AS abpqty,
        NULL AS aborsd,
        COALESCE(c.hcitem, 'UNKNOWN') AS trade_type,
        NULL AS aakind,
        NULL AS aadisp,
        COALESCE(c.hcrout, 'UNKNOWN') AS aarout,
        a.fasqty AS amt
    FROM asfa a
    LEFT JOIN ashc c ON a.favenn = c.hcvenn AND a.facstn = c.hccstn AND a.farout = c.hcrout
    WHERE a.fadate >= '1070101'
      AND a.fadate <= '1131231'
    
    UNION ALL
    
    -- ASFF 資料 - 簡化處理
    SELECT 
        SUBSTR(a.ffdate, 1, 5) AS abyymm,
        a.ffvenn AS abvenn,
        a.ffdate AS abdate,
        a.ffitno AS abitno,
        a.ffoqty * 12 AS aboqty,  -- 打數轉瓶罐
        0 AS abtxat,
        a.ffamte AS abamte,
        0 AS abrqty,
        0 AS abrtxa,
        0 AS abramt,
        0 AS abpqty,
        NULL AS aborsd,
        COALESCE(c.hcitem, 'EX') AS trade_type,
        NULL AS aakind,
        NULL AS aadisp,
        COALESCE(c.hcrout, 'UNKNOWN') AS aarout,
        a.ffoqty * 12 AS amt
    FROM asff a
    LEFT JOIN ashc c ON a.ffvenn = c.hcvenn
    WHERE a.ffdate >= '1070101'
      AND a.ffdate <= '1131231'
)
GROUP BY 
    SUBSTR(abyymm, 1, 3) + 1911 || SUBSTR(abyymm, 4, 2) || '01',
    abvenn,
    SUBSTR(abdate, 1, 3) + 1911 || SUBSTR(abdate, 4, 2) || SUBSTR(abdate, 6, 2),
    abitno,
    aborsd,
    trade_type,
    aakind,
    aadisp,
    aarout,
    SUBSTR(abyymm, 1, 3) + 1911 || SUBSTR(abyymm, 4, 2) || abitno,
    abvenn || aarout || trade_type;

-- 方案 2: 如果還是太慢，使用分年度查詢
-- 2024年資料 (113年)
SELECT /*+ PARALLEL(4) */
    -- 相同的 SELECT 欄位
FROM (
    -- 相同的 UNION ALL 結構
    -- 但只查詢 113 年資料
    WHERE a.abdate >= '1130101' AND a.abdate <= '1131231'
)
-- 相同的 GROUP BY

UNION ALL

-- 2023年資料 (112年)
-- ... 依此類推

-- 方案 3: 極簡版本 - 只計算必要欄位
SELECT /*+ PARALLEL(8) */
    SUBSTR(abdate, 1, 5) AS abyymm,
    abvenn,
    abitno,
    COUNT(*) AS record_count,
    SUM(aboqty + abpqty - abrqty) AS total_qty
FROM asab
WHERE abdate >= '1070101' 
  AND abdate <= '1131231'
GROUP BY SUBSTR(abdate, 1, 5), abvenn, abitno

UNION ALL

SELECT 
    SUBSTR(fadate, 1, 5) AS abyymm,
    favenn AS abvenn,
    faitno AS abitno,
    COUNT(*) AS record_count,
    SUM(fasqty) AS total_qty
FROM asfa
WHERE fadate >= '1070101' 
  AND fadate <= '1131231'
GROUP BY SUBSTR(fadate, 1, 5), favenn, faitno

UNION ALL

SELECT 
    SUBSTR(ffdate, 1, 5) AS abyymm,
    ffvenn AS abvenn,
    ffitno AS abitno,
    COUNT(*) AS record_count,
    SUM(ffoqty * 12) AS total_qty
FROM asff
WHERE ffdate >= '1070101' 
  AND ffdate <= '1131231'
GROUP BY SUBSTR(ffdate, 1, 5), ffvenn, ffitno;
